import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart' as auth;
import '../models/user.dart';

class FirebaseService {
  final auth.FirebaseAuth _firebaseAuth = auth.FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get current user stream
  Stream<auth.User?> get authStateChanges => _firebaseAuth.authStateChanges();

  // Get current user from firebase auth
  auth.User? get currentUser => _firebaseAuth.currentUser;

  // Sign up with email and password
  Future<auth.UserCredential> signUpWithEmailAndPassword(String email, String password) async {
    return await _firebaseAuth.createUserWithEmailAndPassword(email: email, password: password);
  }

  // Sign in with email and password
  Future<auth.UserCredential> signInWithEmailAndPassword(String email, String password) async {
    return await _firebaseAuth.signInWithEmailAndPassword(email: email, password: password);
  }

  // Sign out
  Future<void> signOut() async {
    await _firebaseAuth.signOut();
  }

  // Create user in Firestore
  Future<void> createUserInFirestore({
    required String uid,
    required String name,
    required String email,
    required String phone,
    required UserRole role,
  }) async {
    final user = User(
      id: uid,
      name: name,
      email: email,
      phone: phone,
      role: role,
      createdAt: DateTime.now(),
      lastLoginAt: DateTime.now(),
      favoritePropertyIds: [],
      preferences: UserPreferences.initial(),
    );
    await _firestore.collection('users').doc(uid).set(user.toJson());
  }

  // Get user details from Firestore
  Future<User?> getUserDetails(String uid) async {
    try {
      final doc = await _firestore.collection('users').doc(uid).get();
      if (doc.exists) {
        return User.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      print('Error getting user details: $e');
      return null;
    }
  }

  // Update user profile in Firestore
  Future<void> updateUserProfile(User user) async {
    await _firestore.collection('users').doc(user.id).update(user.toJson());
  }

  Future<void> updateUserFavorites(String userId, List<String> favoritePropertyIds) async {
    await _firestore.collection('users').doc(userId).update({
      'favoritePropertyIds': favoritePropertyIds,
    });
  }

  Future<void> updateUserPreferences(String userId, UserPreferences preferences) async {
    await _firestore.collection('users').doc(userId).update({
      'preferences': preferences.toJson(),
    });
  }

  Future<List<Property>> getPropertiesByIds(List<String> ids) async {
    if (ids.isEmpty) return [];

    final properties = <Property>[];
    // In a real app, you'd use a 'whereIn' query to fetch all documents in one go.
    // For example: _firestore.collection('properties').where(FieldPath.documentId, whereIn: ids).get();
    // Simulating this by fetching one by one for now.
    for (String id in ids) {
      try {
        final property = await getPropertyById(id);
        if (property != null) {
          properties.add(property);
        }
      } catch (e) {
        print('Error fetching property with id $id: $e');
        // Continue fetching other properties
      }
    }
    return properties;
  }
}
