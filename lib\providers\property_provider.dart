import 'package:flutter/foundation.dart';
import '../models/property.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';

class PropertyProvider extends ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  // State variables
  List<Property> _properties = [];
  List<Property> _featuredProperties = [];
  List<Property> _searchResults = [];
  List<Property> _favoriteProperties = [];
  Property? _selectedProperty;
  
  bool _isLoading = false;
  bool _isSearching = false;
  bool _hasError = false;
  String _errorMessage = '';
  
  // Filter state
  PropertyType? _selectedType;
  double _minPrice = 0;
  double _maxPrice = 10000000;
  String? _selectedLocation;
  String _searchQuery = '';
  
  // Pagination
  int _currentPage = 1;
  bool _hasMoreData = true;
  
  // Getters
  List<Property> get properties => _properties;
  List<Property> get featuredProperties => _featuredProperties;
  List<Property> get searchResults => _searchResults;
  List<Property> get favoriteProperties => _favoriteProperties;
  Property? get selectedProperty => _selectedProperty;
  
  bool get isLoading => _isLoading;
  bool get isSearching => _isSearching;
  bool get hasError => _hasError;
  String get errorMessage => _errorMessage;
  
  PropertyType? get selectedType => _selectedType;
  double get minPrice => _minPrice;
  double get maxPrice => _maxPrice;
  String? get selectedLocation => _selectedLocation;
  String get searchQuery => _searchQuery;
  
  int get currentPage => _currentPage;
  bool get hasMoreData => _hasMoreData;
  
  // Initialize provider
  Future<void> initialize() async {
    await loadProperties();
    await loadFeaturedProperties();
    // Favorites are now loaded on demand from the FavoritesScreen
  }
  
  // Load properties with optional filters
  Future<void> loadProperties({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _hasMoreData = true;
      _properties.clear();
    }
    
    if (_isLoading || !_hasMoreData) return;
    
    _setLoading(true);
    _clearError();
    
    try {
      final newProperties = await _apiService.getProperties(
        page: _currentPage,
        location: _selectedLocation,
        type: _selectedType,
        minPrice: _minPrice,
        maxPrice: _maxPrice,
        searchQuery: _searchQuery.isNotEmpty ? _searchQuery : null,
      );
      
      if (newProperties.isEmpty) {
        _hasMoreData = false;
      } else {
        if (refresh) {
          _properties = newProperties;
        } else {
          _properties.addAll(newProperties);
        }
        _currentPage++;
        
        // Cache properties
        await StorageService.cacheProperties(_properties);
      }
    } catch (e) {
      _setError('Failed to load properties: ${e.toString()}');
      
      // Load cached properties if available
      if (_properties.isEmpty) {
        _properties = StorageService.getCachedProperties();
      }
    } finally {
      _setLoading(false);
    }
  }
  
  // Load featured properties
  Future<void> loadFeaturedProperties() async {
    try {
      _featuredProperties = await _apiService.getFeaturedProperties();
      notifyListeners();
    } catch (e) {
      print('Error loading featured properties: $e');
      // Use cached data or mock data
      _featuredProperties = StorageService.getCachedProperties()
          .where((p) => p.isFeatured)
          .take(5)
          .toList();
      notifyListeners();
    }
  }
  
  // Search properties
  Future<void> searchProperties(String query) async {
    if (query.trim().isEmpty) {
      _searchResults.clear();
      _searchQuery = '';
      notifyListeners();
      return;
    }
    
    _setSearching(true);
    _searchQuery = query;
    
    try {
      _searchResults = await _apiService.searchProperties(query);
      
      // Add to search history
      await StorageService.addSearchQuery(query);
    } catch (e) {
      _setError('Search failed: ${e.toString()}');
      _searchResults.clear();
    } finally {
      _setSearching(false);
    }
  }
  
  // Get property by ID
  Future<void> loadPropertyById(String id) async {
    _setLoading(true);
    _clearError();
    
    try {
      _selectedProperty = await _apiService.getPropertyById(id);
      
      if (_selectedProperty != null) {
        // Cache the property
        await StorageService.cacheProperty(_selectedProperty!);
        
        // Add to recently viewed
        await StorageService.addRecentlyViewed(id);
      }
    } catch (e) {
      _setError('Failed to load property: ${e.toString()}');
      
      // Try to get from cache
      _selectedProperty = StorageService.getCachedProperty(id);
    } finally {
      _setLoading(false);
    }
  }
  
  // Favorites management
  Future<void> loadFavoriteProperties(List<String> favoriteIds) async {
    if (favoriteIds.isEmpty) {
      _favoriteProperties = [];
      notifyListeners();
      return;
    }

    _setLoading(true);
    _clearError();

    try {
      _favoriteProperties = await _apiService.getPropertiesByIds(favoriteIds);
    } catch (e) {
      _setError('Failed to load favorite properties: ${e.toString()}');
      _favoriteProperties = [];
    } finally {
      _setLoading(false);
    }
  }
  

  
  // Filter methods
  void setTypeFilter(PropertyType? type) {
    _selectedType = type;
    _resetPagination();
    loadProperties(refresh: true);
  }
  
  void setPriceRange(double min, double max) {
    _minPrice = min;
    _maxPrice = max;
    _resetPagination();
    loadProperties(refresh: true);
  }
  
  void setLocationFilter(String? location) {
    _selectedLocation = location;
    _resetPagination();
    loadProperties(refresh: true);
  }
  
  void clearFilters() {
    _selectedType = null;
    _minPrice = 0;
    _maxPrice = 10000000;
    _selectedLocation = null;
    _searchQuery = '';
    _resetPagination();
    loadProperties(refresh: true);
  }
  
  // Utility methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  void _setSearching(bool searching) {
    _isSearching = searching;
    notifyListeners();
  }
  
  void _setError(String message) {
    _hasError = true;
    _errorMessage = message;
    notifyListeners();
  }
  
  void _clearError() {
    _hasError = false;
    _errorMessage = '';
    notifyListeners();
  }
  
  void _resetPagination() {
    _currentPage = 1;
    _hasMoreData = true;
    _properties.clear();
  }
  
  // Get recently viewed properties
  List<Property> getRecentlyViewed() {
    return StorageService.getRecentlyViewedProperties();
  }
  
  // Get search history
  List<String> getSearchHistory() {
    return StorageService.getSearchHistory();
  }
  
  // Clear search history
  Future<void> clearSearchHistory() async {
    await StorageService.clearSearchHistory();
    notifyListeners();
  }
  
  // Get available locations for filter
  List<String> getAvailableLocations() {
    final locations = _properties.map((p) => p.location).toSet().toList();
    locations.sort();
    return locations;
  }
  
  // Get price range for filter
  Map<String, double> getPriceRange() {
    if (_properties.isEmpty) {
      return {'min': 0, 'max': 10000000};
    }

    final prices = _properties.map((p) => p.price).toList();
    prices.sort();

    return {
      'min': prices.first,
      'max': prices.last,
    };
  }

  // Check if property is favorite
  bool isFavorite(String propertyId) {
    // This would typically check against user's favorites
    // For now, we'll use local storage
    return StorageService.isFavorite(propertyId);
  }

  // Toggle favorite status
  Future<void> toggleFavorite(String propertyId) async {
    if (isFavorite(propertyId)) {
      await StorageService.removeFromFavorites(propertyId);
    } else {
      await StorageService.addToFavorites(propertyId);
    }
    notifyListeners();
  }
}
