import 'package:hive/hive.dart';

part 'property.g.dart';

@HiveType(typeId: 0)
class Property extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String title;

  @HiveField(2)
  final String description;

  @HiveField(3)
  final double price;

  @<PERSON>veField(4)
  final String location;

  @HiveField(5)
  final String address;

  @HiveField(6)
  final int bedrooms;

  @HiveField(7)
  final int bathrooms;

  @HiveField(8)
  final double area;

  @HiveField(9)
  final List<String> images;

  @HiveField(10)
  final PropertyType type;

  @HiveField(11)
  final PropertyStatus status;

  @HiveField(12)
  final double latitude;

  @HiveField(13)
  final double longitude;

  @HiveField(14)
  final String agentName;

  @HiveField(15)
  final String agentPhone;

  @HiveField(16)
  final String agentEmail;

  @HiveField(17)
  final DateTime createdAt;

  @HiveField(18)
  final DateTime updatedAt;

  @HiveField(19)
  final List<String> amenities;

  @HiveField(20)
  final bool isFeatured;

  @<PERSON>veField(21)
  final int? yearBuilt;

  @HiveField(22)
  final int? lotSize;

  @HiveField(23)
  final String ownerId;

  @HiveField(24)
  final String ownerName;

  @HiveField(25)
  final String ownerEmail;

  @HiveField(26)
  final String ownerPhone;

  Property({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.location,
    required this.address,
    required this.bedrooms,
    required this.bathrooms,
    required this.area,
    required this.images,
    required this.type,
    required this.status,
    required this.latitude,
    required this.longitude,
    required this.agentName,
    required this.agentPhone,
    required this.agentEmail,
    required this.createdAt,
    required this.updatedAt,
    required this.amenities,
    required this.ownerId,
    required this.ownerName,
    required this.ownerEmail,
    required this.ownerPhone,
    this.isFeatured = false,
    this.yearBuilt,
    this.lotSize,
  });

  factory Property.fromJson(Map<String, dynamic> json) {
    return Property(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      location: json['location'] ?? '',
      address: json['address'] ?? '',
      bedrooms: json['bedrooms'] ?? 0,
      bathrooms: json['bathrooms'] ?? 0,
      area: (json['area'] ?? 0).toDouble(),
      images: List<String>.from(json['images'] ?? []),
      type: PropertyType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => PropertyType.house,
      ),
      status: PropertyStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => PropertyStatus.available,
      ),
      latitude: (json['latitude'] ?? 0).toDouble(),
      longitude: (json['longitude'] ?? 0).toDouble(),
      agentName: json['agentName'] ?? '',
      agentPhone: json['agentPhone'] ?? '',
      agentEmail: json['agentEmail'] ?? '',
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
      amenities: List<String>.from(json['amenities'] ?? []),
      isFeatured: json['isFeatured'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'price': price,
      'location': location,
      'address': address,
      'bedrooms': bedrooms,
      'bathrooms': bathrooms,
      'area': area,
      'images': images,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'latitude': latitude,
      'longitude': longitude,
      'agentName': agentName,
      'agentPhone': agentPhone,
      'agentEmail': agentEmail,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'amenities': amenities,
      'isFeatured': isFeatured,
    };
  }

  String get formattedPrice {
    if (price >= 1000000) {
      return '\$${(price / 1000000).toStringAsFixed(1)}M';
    } else if (price >= 1000) {
      return '\$${(price / 1000).toStringAsFixed(0)}K';
    } else {
      return '\$${price.toStringAsFixed(0)}';
    }
  }

  String get propertyDetails {
    return '$bedrooms bed • $bathrooms bath • ${area.toInt()} sqft';
  }

  // Getters for compatibility with new screens
  List<String> get imageUrls => images;
  int get squareFeet => area.toInt();

  Property copyWith({
    String? id,
    String? title,
    String? description,
    double? price,
    String? location,
    String? address,
    int? bedrooms,
    int? bathrooms,
    double? area,
    List<String>? images,
    PropertyType? type,
    PropertyStatus? status,
    double? latitude,
    double? longitude,
    String? agentName,
    String? agentPhone,
    String? agentEmail,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? amenities,
    bool? isFeatured,
    int? yearBuilt,
    int? lotSize,
    String? ownerId,
    String? ownerName,
    String? ownerEmail,
    String? ownerPhone,
  }) {
    return Property(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      price: price ?? this.price,
      location: location ?? this.location,
      address: address ?? this.address,
      bedrooms: bedrooms ?? this.bedrooms,
      bathrooms: bathrooms ?? this.bathrooms,
      area: area ?? this.area,
      images: images ?? this.images,
      type: type ?? this.type,
      status: status ?? this.status,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      agentName: agentName ?? this.agentName,
      agentPhone: agentPhone ?? this.agentPhone,
      agentEmail: agentEmail ?? this.agentEmail,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      amenities: amenities ?? this.amenities,
      isFeatured: isFeatured ?? this.isFeatured,
      yearBuilt: yearBuilt ?? this.yearBuilt,
      lotSize: lotSize ?? this.lotSize,
      ownerId: ownerId ?? this.ownerId,
      ownerName: ownerName ?? this.ownerName,
      ownerEmail: ownerEmail ?? this.ownerEmail,
      ownerPhone: ownerPhone ?? this.ownerPhone,
    );
  }
}

@HiveType(typeId: 1)
enum PropertyType {
  @HiveField(0)
  house,
  @HiveField(1)
  apartment,
  @HiveField(2)
  condo,
  @HiveField(3)
  townhouse,
  @HiveField(4)
  villa,
  @HiveField(5)
  land,
}

@HiveType(typeId: 2)
enum PropertyStatus {
  @HiveField(0)
  available,
  @HiveField(1)
  sold,
  @HiveField(2)
  pending,
  @HiveField(3)
  rented,
  @HiveField(4)
  active,
  @HiveField(5)
  inactive,
}

extension PropertyTypeExtension on PropertyType {
  String get displayName {
    switch (this) {
      case PropertyType.house:
        return 'House';
      case PropertyType.apartment:
        return 'Apartment';
      case PropertyType.condo:
        return 'Condo';
      case PropertyType.townhouse:
        return 'Townhouse';
      case PropertyType.villa:
        return 'Villa';
      case PropertyType.land:
        return 'Land';
    }
  }
}

extension PropertyStatusExtension on PropertyStatus {
  String get displayName {
    switch (this) {
      case PropertyStatus.available:
        return 'Available';
      case PropertyStatus.sold:
        return 'Sold';
      case PropertyStatus.pending:
        return 'Pending';
      case PropertyStatus.rented:
        return 'Rented';
      case PropertyStatus.active:
        return 'Active';
      case PropertyStatus.inactive:
        return 'Inactive';
    }
  }
}

extension PropertyStatusExtension on PropertyStatus {
  String get displayName {
    switch (this) {
      case PropertyStatus.available:
        return 'Available';
      case PropertyStatus.sold:
        return 'Sold';
      case PropertyStatus.pending:
        return 'Pending';
      case PropertyStatus.rented:
        return 'Rented';
    }
  }
}
