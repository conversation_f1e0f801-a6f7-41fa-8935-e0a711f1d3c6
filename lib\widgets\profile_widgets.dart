import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../providers/user_provider.dart';
import '../providers/app_provider.dart';
import '../models/property.dart';

class ProfileWidgets {
  static Widget buildStatsSection(BuildContext context, UserProvider userProvider) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              context,
              'Favorites',
              '${userProvider.getFavoriteIds().length}',
              Icons.favorite,
              Colors.red,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              context,
              'Viewed',
              '12',
              Icons.visibility,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              context,
              'Saved',
              '8',
              Icons.bookmark,
              Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  static Widget _buildStatCard(BuildContext context, String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  static Widget buildMenuSection(BuildContext context, UserProvider userProvider, AppProvider appProvider) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Account',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                _buildMenuItem(
                  context,
                  Icons.person_outline,
                  'Edit Profile',
                  'Update your personal information',
                  () => _showEditProfileDialog(context, userProvider),
                ),
                _buildDivider(),
                _buildMenuItem(
                  context,
                  Icons.favorite_outline,
                  'My Favorites',
                  'View your saved properties',
                  () => Navigator.pushNamed(context, '/favorites'),
                ),
                _buildDivider(),
                _buildMenuItem(
                  context,
                  Icons.history,
                  'Recently Viewed',
                  'Properties you\'ve looked at',
                  () {
                    // Navigate to recently viewed
                  },
                ),
                _buildDivider(),
                _buildMenuItem(
                  context,
                  Icons.notifications,
                  'Notifications',
                  'Manage your notification preferences',
                  () => _showNotificationSettings(context, appProvider),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static Widget buildPreferencesSection(BuildContext context, UserProvider userProvider, AppProvider appProvider) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Preferences',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                _buildSwitchMenuItem(
                  context,
                  Icons.dark_mode,
                  'Dark Mode',
                  'Switch to dark theme',
                  userProvider.currentUser?.preferences.darkMode ?? false,
                  (value) {
                    final newPrefs = userProvider.currentUser!.preferences.copyWith(darkMode: value);
                    userProvider.updatePreferences(newPrefs);
                  },
                ),
                _buildDivider(),
                _buildMenuItem(
                  context,
                  Icons.language,
                  'Language',
                  userProvider.currentUser?.preferences.language.toUpperCase() ?? 'EN',
                  () => _showLanguageSelector(context, userProvider),
                ),
                _buildDivider(),
                _buildMenuItem(
                  context,
                  Icons.attach_money,
                  'Currency',
                  userProvider.currentUser?.preferences.currency ?? 'USD',
                  () => _showCurrencySelector(context, userProvider),
                ),
                _buildDivider(),
                _buildSwitchMenuItem(
                  context,
                  Icons.notifications_active,
                  'Push Notifications',
                  'Receive updates on new properties',
                  userProvider.currentUser?.preferences.notifications ?? true,
                  (value) {
                    final newPrefs = userProvider.currentUser!.preferences.copyWith(notifications: value);
                    userProvider.updatePreferences(newPrefs);
                  },
                ),
                _buildDivider(),
                _buildMenuItem(
                  context,
                  Icons.real_estate_agent,
                  'Property Types',
                  'Select preferred property types',
                  () => _showPropertyTypeSelector(context, userProvider),
                ),
                _buildDivider(),
                _buildMenuItem(
                  context,
                  Icons.location_city,
                  'Locations',
                  'Select preferred locations',
                  () => _showLocationSelector(context, userProvider),
                ),
                _buildDivider(),
                _buildPriceRange(context, userProvider),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static Widget buildLogoutSection(BuildContext context, UserProvider userProvider) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Account Actions',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                _buildMenuItem(
                  context,
                  Icons.help_outline,
                  'Help & Support',
                  'Get help with the app',
                  () {
                    // Show help
                  },
                ),
                _buildDivider(),
                _buildMenuItem(
                  context,
                  Icons.privacy_tip,
                  'Privacy Policy',
                  'Read our privacy policy',
                  () {
                    // Show privacy policy
                  },
                ),
                _buildDivider(),
                _buildMenuItem(
                  context,
                  Icons.logout,
                  'Logout',
                  'Sign out of your account',
                  () => _showLogoutDialog(context, userProvider),
                  isDestructive: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  static Widget _buildMenuItem(
    BuildContext context,
    IconData icon,
    String title,
    String subtitle,
    VoidCallback onTap, {
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isDestructive 
              ? Colors.red.withValues(alpha: 0.1)
              : Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: isDestructive ? Colors.red : Theme.of(context).colorScheme.primary,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.w600,
          color: isDestructive ? Colors.red : null,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey[600],
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: Colors.grey[400],
      ),
      onTap: onTap,
    );
  }

  static Widget _buildSwitchMenuItem(
    BuildContext context,
    IconData icon,
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey[600],
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
      ),
    );
  }

  static Widget _buildDivider() {
    return Divider(
      height: 1,
      indent: 16,
      endIndent: 16,
      color: Colors.grey[200],
    );
  }

  // Dialog methods
  static void _showLogoutDialog(BuildContext context, UserProvider userProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to log out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context); // Close the dialog
              await userProvider.logout();
              Navigator.of(context).pushNamedAndRemoveUntil('/auth', (route) => false);
            },
            child: const Text('Logout'),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
          ),
        ],
      ),
    );
  }

  static void _showEditProfileDialog(BuildContext context, UserProvider userProvider) {
    final nameController = TextEditingController(text: userProvider.currentUser?.name ?? '');
    final emailController = TextEditingController(text: userProvider.currentUser?.email ?? '');
    final phoneController = TextEditingController(text: userProvider.currentUser?.phone ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Profile'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: emailController,
              decoration: const InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              await userProvider.updateProfile(
                name: nameController.text,
                email: emailController.text,
                phone: phoneController.text,
              );
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  static void _showLogoutDialog(BuildContext context, UserProvider userProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              await userProvider.logout();
              Navigator.pop(context);
              Navigator.pushReplacementNamed(context, '/');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Logout'),
          ),
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Language'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('English'),
              onTap: () {
                final newPrefs = userProvider.currentUser!.preferences.copyWith(language: 'en');
                userProvider.updatePreferences(newPrefs);
                Navigator.pop(context);
              },
            ),
            ListTile(
              title: const Text('Spanish'),
              onTap: () {
                final newPrefs = userProvider.currentUser!.preferences.copyWith(language: 'es');
                userProvider.updatePreferences(newPrefs);
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  static void _showCurrencySelector(BuildContext context, UserProvider userProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Currency'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('USD'),
              onTap: () {
                final newPrefs = userProvider.currentUser!.preferences.copyWith(currency: 'USD');
                userProvider.updatePreferences(newPrefs);
                Navigator.pop(context);
              },
            ),
            ListTile(
              title: const Text('EUR'),
              onTap: () {
                final newPrefs = userProvider.currentUser!.preferences.copyWith(currency: 'EUR');
                userProvider.updatePreferences(newPrefs);
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  static void _showNotificationSettings(BuildContext context, UserProvider userProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notification Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: const Text('New Property Alerts'),
              value: userProvider.currentUser?.preferences.notifications ?? true,
              onChanged: (value) {
                 final newPrefs = userProvider.currentUser!.preferences.copyWith(notifications: value);
                 userProvider.updatePreferences(newPrefs);
              },
            ),
            SwitchListTile(
              title: const Text('Price Change Alerts'),
              value: userProvider.currentUser?.preferences.notifications ?? true,
              onChanged: (value) {
                 final newPrefs = userProvider.currentUser!.preferences.copyWith(notifications: value);
                 userProvider.updatePreferences(newPrefs);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  static Widget _buildPriceRange(BuildContext context, UserProvider userProvider) {
    final preferences = userProvider.currentUser!.preferences;
    final min = preferences.minPrice ?? 0;
    final max = preferences.maxPrice ?? 1000000;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Price Range', style: Theme.of(context).textTheme.titleMedium),
          RangeSlider(
            values: RangeValues(min, max),
            min: 0,
            max: 5000000,
            divisions: 50,
            labels: RangeLabels('\$${min.round()}', '\$${max.round()}'),
            onChanged: (values) {
              final newPrefs = preferences.copyWith(minPrice: values.start, maxPrice: values.end);
              userProvider.updatePreferences(newPrefs);
            },
          ),
        ],
      ),
    );
  }

  static void _showPropertyTypeSelector(BuildContext context, UserProvider userProvider) {
    final preferences = userProvider.currentUser!.preferences;
    List<PropertyType> selectedTypes = List.from(preferences.preferredPropertyTypes);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Property Types'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: PropertyType.values.map((type) {
                  return CheckboxListTile(
                    title: Text(type.toString().split('.').last),
                    value: selectedTypes.contains(type),
                    onChanged: (checked) {
                      setState(() {
                        if (checked!) {
                          selectedTypes.add(type);
                        } else {
                          selectedTypes.remove(type);
                        }
                      });
                    },
                  );
                }).toList(),
              ),
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final newPrefs = preferences.copyWith(preferredPropertyTypes: selectedTypes);
              userProvider.updatePreferences(newPrefs);
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  static void _showLocationSelector(BuildContext context, UserProvider userProvider) {
    final preferences = userProvider.currentUser!.preferences;
    List<String> selectedLocations = List.from(preferences.preferredLocations);
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Locations'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return SizedBox(
              width: double.maxFinite,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: controller,
                    decoration: InputDecoration(
                      labelText: 'Add a location',
                      suffixIcon: IconButton(
                        icon: const Icon(Icons.add),
                        onPressed: () {
                          if (controller.text.isNotEmpty) {
                            setState(() {
                              selectedLocations.add(controller.text);
                              controller.clear();
                            });
                          }
                        },
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Wrap(
                    spacing: 8,
                    runSpacing: 4,
                    children: selectedLocations.map((location) {
                      return Chip(
                        label: Text(location),
                        onDeleted: () {
                          setState(() {
                            selectedLocations.remove(location);
                          });
                        },
                      );
                    }).toList(),
                  ),
                ],
              ),
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final newPrefs = preferences.copyWith(preferredLocations: selectedLocations);
              userProvider.updatePreferences(newPrefs);
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }
}
