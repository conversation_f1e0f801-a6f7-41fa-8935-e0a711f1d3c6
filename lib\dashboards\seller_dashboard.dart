import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/user.dart';
import '../models/property.dart';
import '../models/analytics.dart';
import '../providers/user_provider.dart';
import '../providers/property_provider.dart';
import 'dashboard_router.dart';
import '../widgets/property_card_widgets.dart';

class SellerDashboard extends BaseDashboard {
  const SellerDashboard({Key? key}) : super(key: key);

  @override
  State<SellerDashboard> createState() => _SellerDashboardState();
}

class _SellerDashboardState extends BaseDashboardState<SellerDashboard> {
  @override
  Widget buildAppBar(BuildContext context, String title, {List<Widget>? actions}) {
    return AppBar(
      title: Text(title),
      backgroundColor: DashboardNavigationHelper.getPrimaryColor(UserRole.seller),
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.add),
          onPressed: () => _showAddPropertyDialog(context),
        ),
        IconButton(
          icon: const Icon(Icons.notifications_outlined),
          onPressed: () => _showNotifications(context),
        ),
        ...?actions,
      ],
    );
  }

  @override
  Widget buildBody(BuildContext context) {
    return Consumer2<UserProvider, PropertyProvider>(
      builder: (context, userProvider, propertyProvider, child) {
        final user = userProvider.currentUser!;
        
        return TabBarView(
          controller: tabController,
          children: [
            _buildOverviewTab(context, user, propertyProvider),
            _buildPropertiesTab(context, user, propertyProvider),
            _buildAnalyticsTab(context, user, propertyProvider),
            _buildInquiriesTab(context, user, propertyProvider),
          ],
        );
      },
    );
  }

  Widget _buildOverviewTab(BuildContext context, User user, PropertyProvider propertyProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(user),
          const SizedBox(height: 16),
          _buildQuickStats(propertyProvider),
          const SizedBox(height: 16),
          _buildRecentActivity(),
          const SizedBox(height: 16),
          _buildQuickActions(context),
        ],
      ),
    );
  }

  Widget _buildWelcomeCard(User user) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 800),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.8 + (0.2 * value),
          child: Opacity(
            opacity: value,
            child: Card(
              elevation: 8,
              shadowColor: DashboardNavigationHelper.getPrimaryColor(UserRole.seller).withOpacity(0.3),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      DashboardNavigationHelper.getPrimaryColor(UserRole.seller),
                      DashboardNavigationHelper.getPrimaryColor(UserRole.seller).withOpacity(0.8),
                      DashboardNavigationHelper.getPrimaryColor(UserRole.seller).withOpacity(0.6),
                    ],
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Icon(
                        Icons.home_work,
                        color: Colors.white,
                        size: 32,
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Welcome back, ${user.name}!',
                            style: const TextStyle(
                              fontSize: 26,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Manage your property listings and track performance',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickStats(PropertyProvider propertyProvider) {
    // Mock data - in real app, this would come from analytics provider
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Active Listings',
            '12',
            Icons.home_work,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Total Views',
            '1,234',
            Icons.visibility,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Inquiries',
            '45',
            Icons.message,
            Colors.orange,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Sold',
            '3',
            Icons.check_circle,
            Colors.purple,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1000),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Card(
          elevation: 4 * value,
          shadowColor: color.withOpacity(0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(
              color: color.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white,
                  color.withOpacity(0.05),
                ],
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: color, size: 28),
                ),
                const SizedBox(height: 16),
                TweenAnimationBuilder<double>(
                  duration: const Duration(milliseconds: 1500),
                  curve: Curves.easeOutCubic,
                  tween: Tween(begin: 0.0, end: double.tryParse(value.replaceAll(',', '')) ?? 0),
                  builder: (context, animValue, child) {
                    // Format the animated value similar to the original value
                    String formattedValue = value.contains(',')
                        ? animValue.toInt().toString().replaceAllMapped(
                            RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                            (Match m) => '${m[1]},')
                        : animValue.toInt().toString();

                    return Text(
                      formattedValue,
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: color.withOpacity(0.8),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[700],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentActivity() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Recent Activity',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildActivityItem(
              'New inquiry for Modern Villa',
              '2 hours ago',
              Icons.message,
              Colors.blue,
            ),
            _buildActivityItem(
              'Property viewed 15 times today',
              '4 hours ago',
              Icons.visibility,
              Colors.green,
            ),
            _buildActivityItem(
              'Price updated for Downtown Condo',
              '1 day ago',
              Icons.edit,
              Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(String title, String time, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
                Text(time, style: TextStyle(color: Colors.grey[600], fontSize: 12)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Card(
      elevation: 4,
      shadowColor: Colors.grey.withOpacity(0.2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: DashboardNavigationHelper.getPrimaryColor(UserRole.seller).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.flash_on,
                    color: DashboardNavigationHelper.getPrimaryColor(UserRole.seller),
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Quick Actions',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    'Add Property',
                    Icons.add_home,
                    () => Navigator.pushNamed(context, '/add_property'),
                    color: Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildActionButton(
                    'View Analytics',
                    Icons.analytics,
                    () => tabController.animateTo(2),
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    'Manage Inquiries',
                    Icons.message,
                    () => tabController.animateTo(3),
                    color: Colors.orange,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildActionButton(
                    'Update Prices',
                    Icons.price_change,
                    () => _showPriceUpdateDialog(context),
                    color: Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(String label, IconData icon, VoidCallback onPressed, {required Color color}) {
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon),
        label: Text(label),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white,
          foregroundColor: color,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(color: color.withOpacity(0.3)),
          ),
          elevation: 0,
        ),
      ),
    );
  }

  Widget _buildPropertiesTab(BuildContext context, User user, PropertyProvider propertyProvider) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: const InputDecoration(
                    hintText: 'Search your properties...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    // Implement search
                  },
                ),
              ),
              const SizedBox(width: 12),
              IconButton(
                onPressed: () => _showFilterDialog(context),
                icon: const Icon(Icons.filter_list),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: 5, // Mock data
            itemBuilder: (context, index) {
              return _buildPropertyListItem(context, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPropertyListItem(BuildContext context, int index) {
    // Create a mock property for demonstration
    final mockProperty = Property(
      id: 'property_$index',
      title: 'Property ${index + 1}',
      description: 'Beautiful property in a great location',
      price: 500000.0 + (index * 50000),
      location: '123 Main St, City, State 12345',
      address: '123 Main St',
      type: PropertyType.house,
      status: PropertyStatus.active,
      bedrooms: 3 + index,
      bathrooms: 2 + index,
      area: 2000.0 + (index * 200),
      images: [],
      latitude: 40.7128,
      longitude: -74.0060,
      agentName: 'John Doe',
      agentPhone: '+1234567890',
      agentEmail: '<EMAIL>',
      lotSize: 5000,
      yearBuilt: 2020 - index,
      amenities: ['Swimming Pool', 'Garage', 'Garden'],
      ownerId: 'owner_123',
      ownerName: 'John Doe',
      ownerEmail: '<EMAIL>',
      ownerPhone: '+1234567890',
      createdAt: DateTime.now().subtract(Duration(days: index * 10)),
      updatedAt: DateTime.now().subtract(Duration(days: index * 5)),
    );

    return StatefulBuilder(
      builder: (context, setState) {
        bool isHovered = false;

        return MouseRegion(
          onEnter: (_) => setState(() => isHovered = true),
          onExit: (_) => setState(() => isHovered = false),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: isHovered
                      ? Colors.green.withOpacity(0.2)
                      : Colors.grey.withOpacity(0.1),
                  blurRadius: isHovered ? 8 : 4,
                  spreadRadius: isHovered ? 2 : 0,
                  offset: const Offset(0, 2),
                ),
              ],
              border: Border.all(
                color: isHovered
                    ? DashboardNavigationHelper.getPrimaryColor(UserRole.seller).withOpacity(0.3)
                    : Colors.grey.withOpacity(0.1),
                width: isHovered ? 2 : 1,
              ),
            ),
            child: InkWell(
              borderRadius: BorderRadius.circular(16),
              onTap: () => _viewPropertyDetails(context, mockProperty),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Hero(
                      tag: 'property_image_${mockProperty.id}',
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: Colors.grey[200],
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.2),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: mockProperty.images.isNotEmpty
                              ? Image.network(
                                  mockProperty.images.first,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) => const Icon(
                                    Icons.home,
                                    color: Colors.grey,
                                    size: 40,
                                  ),
                                )
                              : const Icon(
                                  Icons.home,
                                  color: Colors.grey,
                                  size: 40,
                                ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  'Property ${index + 1}',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Colors.green.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(
                                  'Active',
                                  style: TextStyle(
                                    color: Colors.green[700],
                                    fontWeight: FontWeight.w500,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Text(
                                '\$${(500000 + index * 50000).toString()}',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: DashboardNavigationHelper.getPrimaryColor(UserRole.seller),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                '${mockProperty.bedrooms} bed • ${mockProperty.bathrooms} bath • ${mockProperty.squareFeet} sqft',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  mockProperty.location,
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 14,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              _buildPropertyActionButton(
                                'Edit',
                                Icons.edit,
                                () => _editProperty(context, mockProperty),
                                Colors.blue,
                              ),
                              const SizedBox(width: 8),
                              _buildPropertyActionButton(
                                'Analytics',
                                Icons.analytics,
                                () => _viewPropertyDetails(context, mockProperty),
                                Colors.orange,
                              ),
                              const SizedBox(width: 8),
                              _buildPropertyActionButton(
                                'Deactivate',
                                Icons.visibility_off,
                                () => _deactivateProperty(context, mockProperty),
                                Colors.red,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPropertyActionButton(String label, IconData icon, VoidCallback onPressed, Color color) {
    return Expanded(
      child: Container(
        height: 36,
        child: ElevatedButton.icon(
          onPressed: onPressed,
          icon: Icon(icon, size: 16),
          label: Text(
            label,
            style: const TextStyle(fontSize: 12),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: color.withOpacity(0.1),
            foregroundColor: color,
            elevation: 0,
            padding: const EdgeInsets.symmetric(horizontal: 8),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAnalyticsTab(BuildContext context, User user, PropertyProvider propertyProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Performance Analytics',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildAnalyticsChart(),
          const SizedBox(height: 16),
          _buildAnalyticsGrid(),
        ],
      ),
    );
  }

  Widget _buildAnalyticsChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Views Over Time',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(show: false),
                  titlesData: FlTitlesData(show: false),
                  borderData: FlBorderData(show: false),
                  lineBarsData: [
                    LineChartBarData(
                      spots: [
                        const FlSpot(0, 3),
                        const FlSpot(1, 1),
                        const FlSpot(2, 4),
                        const FlSpot(3, 2),
                        const FlSpot(4, 5),
                        const FlSpot(5, 3),
                        const FlSpot(6, 4),
                      ],
                      isCurved: true,
                      color: DashboardNavigationHelper.getPrimaryColor(UserRole.seller),
                      barWidth: 3,
                      dotData: FlDotData(show: false),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalyticsGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      children: [
        _buildAnalyticsCard('Total Revenue', '\$2.5M', Icons.attach_money, Colors.green),
        _buildAnalyticsCard('Avg. Days on Market', '45', Icons.calendar_today, Colors.blue),
        _buildAnalyticsCard('Conversion Rate', '12%', Icons.trending_up, Colors.orange),
        _buildAnalyticsCard('Top Location', 'Downtown', Icons.location_on, Colors.purple),
      ],
    );
  }

  Widget _buildAnalyticsCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            Text(
              title,
              style: TextStyle(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInquiriesTab(BuildContext context, User user, PropertyProvider propertyProvider) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Text(
                'Property Inquiries',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Chip(
                label: const Text('5 New'),
                backgroundColor: Colors.red[100],
                labelStyle: TextStyle(color: Colors.red[800]),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: 8, // Mock data
            itemBuilder: (context, index) {
              return _buildInquiryItem(context, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildInquiryItem(BuildContext context, int index) {
    final isNew = index < 2;
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: isNew ? Colors.red[100] : Colors.grey[200],
          child: Icon(
            Icons.person,
            color: isNew ? Colors.red[800] : Colors.grey[600],
          ),
        ),
        title: Text('John Doe ${index + 1}'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Interested in Modern Villa'),
            Text('2 hours ago', style: TextStyle(color: Colors.grey[600])),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isNew)
              Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
              ),
            const SizedBox(width: 8),
            const Icon(Icons.arrow_forward_ios, size: 16),
          ],
        ),
        onTap: () => _showInquiryDetails(context, index),
      ),
    );
  }

  void _showAddPropertyDialog(BuildContext context) {
    // Navigate to add property screen
    Navigator.pushNamed(context, '/add_property');
  }

  void _showNotifications(BuildContext context) {
    // Show notifications
  }

  void _showFilterDialog(BuildContext context) {
    // Show filter dialog
  }

  void _showPriceUpdateDialog(BuildContext context) {
    // Show price update dialog
  }

  void _handlePropertyAction(String action, int index, Property property) {
    switch (action) {
      case 'edit':
        _editProperty(context, property);
        break;
      case 'analytics':
        _viewPropertyDetails(context, property);
        break;
      case 'deactivate':
        _deactivateProperty(context, property);
        break;
    }
  }

  void _viewPropertyDetails(BuildContext context, Property property) {
    Navigator.pushNamed(
      context,
      '/property_details',
      arguments: property,
    );
  }

  void _editProperty(BuildContext context, Property property) {
    Navigator.pushNamed(
      context,
      '/edit_property',
      arguments: property,
    );
  }

  void _deactivateProperty(BuildContext context, Property property) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Deactivate Property'),
        content: Text('Are you sure you want to deactivate "${property.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Property deactivated successfully'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('Deactivate'),
          ),
        ],
      ),
    );
  }

  void _showInquiryDetails(BuildContext context, int index) {
    Navigator.pushNamed(
      context,
      '/inquiry_details',
      arguments: 'inquiry_$index',
    );
  }
}
