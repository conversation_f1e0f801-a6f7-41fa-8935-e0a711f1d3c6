class PropertyAnalytics {
  final String propertyId;
  final int views;
  final int inquiries;
  final int favorites;
  final int shares;
  final List<ViewData> viewHistory;
  final List<InquiryData> inquiryHistory;
  final DateTime lastUpdated;

  PropertyAnalytics({
    required this.propertyId,
    required this.views,
    required this.inquiries,
    required this.favorites,
    required this.shares,
    required this.viewHistory,
    required this.inquiryHistory,
    required this.lastUpdated,
  });

  factory PropertyAnalytics.fromJson(Map<String, dynamic> json) {
    return PropertyAnalytics(
      propertyId: json['propertyId'] ?? '',
      views: json['views'] ?? 0,
      inquiries: json['inquiries'] ?? 0,
      favorites: json['favorites'] ?? 0,
      shares: json['shares'] ?? 0,
      viewHistory: (json['viewHistory'] as List<dynamic>?)
          ?.map((e) => ViewData.fromJson(e))
          .toList() ?? [],
      inquiryHistory: (json['inquiryHistory'] as List<dynamic>?)
          ?.map((e) => InquiryData.from<PERSON><PERSON>(e))
          .toList() ?? [],
      lastUpdated: DateTime.parse(json['lastUpdated'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'propertyId': propertyId,
      'views': views,
      'inquiries': inquiries,
      'favorites': favorites,
      'shares': shares,
      'viewHistory': viewHistory.map((e) => e.toJson()).toList(),
      'inquiryHistory': inquiryHistory.map((e) => e.toJson()).toList(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }
}

class ViewData {
  final String userId;
  final DateTime timestamp;
  final String source; // 'search', 'featured', 'direct', etc.

  ViewData({
    required this.userId,
    required this.timestamp,
    required this.source,
  });

  factory ViewData.fromJson(Map<String, dynamic> json) {
    return ViewData(
      userId: json['userId'] ?? '',
      timestamp: DateTime.parse(json['timestamp']),
      source: json['source'] ?? 'unknown',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'timestamp': timestamp.toIso8601String(),
      'source': source,
    };
  }
}

class InquiryData {
  final String id;
  final String userId;
  final String userName;
  final String userEmail;
  final String userPhone;
  final String message;
  final InquiryType type;
  final InquiryStatus status;
  final DateTime createdAt;
  final DateTime? respondedAt;

  InquiryData({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userEmail,
    required this.userPhone,
    required this.message,
    required this.type,
    required this.status,
    required this.createdAt,
    this.respondedAt,
  });

  factory InquiryData.fromJson(Map<String, dynamic> json) {
    return InquiryData(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      userName: json['userName'] ?? '',
      userEmail: json['userEmail'] ?? '',
      userPhone: json['userPhone'] ?? '',
      message: json['message'] ?? '',
      type: InquiryType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => InquiryType.general,
      ),
      status: InquiryStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => InquiryStatus.pending,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      respondedAt: json['respondedAt'] != null ? DateTime.parse(json['respondedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'userEmail': userEmail,
      'userPhone': userPhone,
      'message': message,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'createdAt': createdAt.toIso8601String(),
      'respondedAt': respondedAt?.toIso8601String(),
    };
  }
}

enum InquiryType {
  general,
  viewing,
  pricing,
  financing,
  offer,
}

enum InquiryStatus {
  pending,
  responded,
  closed,
}

class DashboardStats {
  final int totalProperties;
  final int activeListings;
  final int soldProperties;
  final int totalViews;
  final int totalInquiries;
  final double totalRevenue;
  final double averagePrice;
  final Map<String, int> propertiesByType;
  final Map<String, int> propertiesByLocation;
  final List<MonthlyData> monthlyData;

  DashboardStats({
    required this.totalProperties,
    required this.activeListings,
    required this.soldProperties,
    required this.totalViews,
    required this.totalInquiries,
    required this.totalRevenue,
    required this.averagePrice,
    required this.propertiesByType,
    required this.propertiesByLocation,
    required this.monthlyData,
  });

  factory DashboardStats.fromJson(Map<String, dynamic> json) {
    return DashboardStats(
      totalProperties: json['totalProperties'] ?? 0,
      activeListings: json['activeListings'] ?? 0,
      soldProperties: json['soldProperties'] ?? 0,
      totalViews: json['totalViews'] ?? 0,
      totalInquiries: json['totalInquiries'] ?? 0,
      totalRevenue: (json['totalRevenue'] ?? 0).toDouble(),
      averagePrice: (json['averagePrice'] ?? 0).toDouble(),
      propertiesByType: Map<String, int>.from(json['propertiesByType'] ?? {}),
      propertiesByLocation: Map<String, int>.from(json['propertiesByLocation'] ?? {}),
      monthlyData: (json['monthlyData'] as List<dynamic>?)
          ?.map((e) => MonthlyData.fromJson(e))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalProperties': totalProperties,
      'activeListings': activeListings,
      'soldProperties': soldProperties,
      'totalViews': totalViews,
      'totalInquiries': totalInquiries,
      'totalRevenue': totalRevenue,
      'averagePrice': averagePrice,
      'propertiesByType': propertiesByType,
      'propertiesByLocation': propertiesByLocation,
      'monthlyData': monthlyData.map((e) => e.toJson()).toList(),
    };
  }
}

class MonthlyData {
  final String month;
  final int properties;
  final int sales;
  final double revenue;
  final int views;
  final int inquiries;

  MonthlyData({
    required this.month,
    required this.properties,
    required this.sales,
    required this.revenue,
    required this.views,
    required this.inquiries,
  });

  factory MonthlyData.fromJson(Map<String, dynamic> json) {
    return MonthlyData(
      month: json['month'] ?? '',
      properties: json['properties'] ?? 0,
      sales: json['sales'] ?? 0,
      revenue: (json['revenue'] ?? 0).toDouble(),
      views: json['views'] ?? 0,
      inquiries: json['inquiries'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'month': month,
      'properties': properties,
      'sales': sales,
      'revenue': revenue,
      'views': views,
      'inquiries': inquiries,
    };
  }
}
